package scraping

import (
	"errors"
	"fmt"
)

type NewsType struct {
	Type   string                   `json:"-"`
	Values map[string]NewsValueType `json:"values"`
}

type NewsValueType struct {
	URL       string `json:"url"`
	Headlines string `json:"headlines"`
	Author    string `json:"author"`
	FullNews  string `json:"fullNews"`
	NewsImg   string `json:"newsImg"`
	NewsURL   string `json:"newsURL"`
	Date      string `json:"date"`
}

type CyberNews struct {
	Extractor *Extractor
	NewsTypes []NewsType
}

func NewCyberNews() *CyberNews {
	extractor := NewExtractor()
	newsTypes := []NewsType{
		{
			Type: "general",
			Values: map[string]NewsValueType{
				"https://ciosea.economictimes.indiatimes.com/news/next-gen-technologies": {
					Headlines: "article.desc div h3.heading",
					Author:    "",
					FullNews:  "article.desc div p.desktop-view",
					NewsImg:   ".desc figure a img",
					NewsURL:   ".desc figure a",
					Date:      "",
					URL:       "https://ciosea.economictimes.indiatimes.com/news/next-gen-technologies",
				},
			},
		},
		{
			Type: "dataBreach",
			Values: map[string]NewsValueType{
				"https://thehackernews.com/search/label/data%20breach": {
					Headlines: "h2.home-title",
					Author:    ".item-label span",
					FullNews:  ".home-desc",
					NewsImg:   ".img-ratio img",
					NewsURL:   "a.story-link",
					Date:      ".item-label",
					URL:       "https://thehackernews.com/search/label/data%20breach",
				},
			},
		},
		// Diğer haber türlerini buraya ekleyin
	}

	return &CyberNews{
		Extractor: extractor,
		NewsTypes: newsTypes,
	}
}

func (c *CyberNews) GetNewsTypes() []string {
	newsTypes := make([]string, 0, len(c.NewsTypes))
	for _, news := range c.NewsTypes {
		for newsType := range news.Values {
			newsTypes = append(newsTypes, newsType)
		}
	}
	return newsTypes
}

func (c *CyberNews) GetNews(news string) ([]NewsValueType, error) {
	fmt.Println("den3")
	for _, newsType := range c.NewsTypes {

		if newsType.Type == news {
			data := c.Extractor.DataExtractor(c.NewsTypes)
			return data, nil
		}

	}
	return nil, errors.New("news type not found")
}
