package scraping

import (
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.com/PuerkitoBio/goquery"
)

type Extractor struct {
	Client      *http.Client
	Headers     http.Header
	Pattern1    *regexp.Regexp
	Pattern2    *regexp.Regexp
	Pattern3    *regexp.Regexp
	Pattern4    *regexp.Regexp
	Pattern5    *regexp.Regexp
	SymbolRegex *regexp.Regexp
	Sorting     Sorting
}

func NewExtractor() *Extractor {
	client := &http.Client{}
	headers := http.Header{}

	// Initialize and compile regex patterns
	pattern1 := regexp.MustCompile(`[^a-zA-Z0-9\s]`)
	pattern2 := regexp.MustCompile(`<[^>]*>`)
	pattern3 := regexp.MustCompile(`[\r\n\t]`)
	pattern4 := regexp.MustCompile(`\b(advertisement|sponsored)\b`)
	pattern5 := regexp.MustCompile(`\b(January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},\s+\d{4}`)
	symbolRegex := regexp.MustCompile(`[^\w\s]`)
	sort := NewSorting()

	return &Extractor{
		Client:      client,
		Headers:     headers,
		Pattern1:    pattern1,
		Pattern2:    pattern2,
		Pattern3:    pattern3,
		Pattern4:    pattern4,
		Pattern5:    pattern5,
		SymbolRegex: symbolRegex,
		Sorting:     *sort,
	}
}

func (e *Extractor) authorNameExtractor(name string) string {
	authorName := e.Pattern1.ReplaceAllString(name, "")
	authorName = e.Pattern2.ReplaceAllString(authorName, "")

	if !e.IsValidAuthorName(authorName) {
		return "N/A"
	}
	return e.FormatAuthorName(authorName)
}

func (e *Extractor) checkAd(newsDate string) bool {
	return e.Pattern4.MatchString(newsDate)
}

func (e *Extractor) newsDateExtractor(date string, newsDate string) string {
	date = e.Pattern2.ReplaceAllString(e.Pattern3.ReplaceAllString(date, ""), "")
	if newsDate != "" {
		return e.Pattern5.FindString(date)
	}
	return "N/A"
}

func (e *Extractor) extractDataFromSingleNews(url string, value NewsValueType) []NewsValueType {
	var newsDataFromSingleNews []NewsValueType
	response, err := e.Client.Get(url)
	if err != nil {
		fmt.Printf("Error retrieving data from URL %s: %v\n", url, err)
		return newsDataFromSingleNews
	}
	defer response.Body.Close()

	doc, err := goquery.NewDocumentFromReader(response.Body)
	if err != nil {
		fmt.Printf("Error parsing HTML response from URL %s: %v\n", url, err)
		return newsDataFromSingleNews
	}

	doc.Find(value.Headlines).Each(func(i int, s *goquery.Selection) {
		newsHeadlines := s.Text()
		rawNewsAuthor := doc.Find(value.Author).Eq(i).Text()
		newsFullNews := doc.Find(value.FullNews).Eq(i).Text()
		newsURL, _ := s.Find(value.NewsURL).Attr("href")
		newsImgURL, _ := s.Find(value.NewsImg).Attr("data-src")
		rawNewsDate := doc.Find(value.Date).Eq(i).Text()

		newsDate := e.newsDateExtractor(rawNewsDate, value.Date)
		newsAuthor := e.authorNameExtractor(rawNewsAuthor)

		if e.checkAd(newsDate) {
			return
		}

		completeNews := NewsValueType{
			Headlines: newsHeadlines,
			Author:    newsAuthor,
			FullNews:  newsFullNews,
			NewsURL:   newsURL,
			NewsImg:   newsImgURL,
			Date:      newsDate,
		}

		newsDataFromSingleNews = append(newsDataFromSingleNews, completeNews)
	})

	return newsDataFromSingleNews
}

func (e *Extractor) DataExtractor(news []NewsType) []NewsValueType {
	var newsData []NewsValueType
	var wg sync.WaitGroup

	for _, singleNews := range news {
		for _, value := range singleNews.Values {
			wg.Add(1)
			go func(value NewsValueType) {
				defer wg.Done()
				newsDataFromSingleNews := e.extractDataFromSingleNews(value.URL, value)
				newsData = append(newsData, newsDataFromSingleNews...)
			}(value)
		}
	}
	wg.Wait()

	return e.Sorting.OrderingNews(newsData)
}

func (p *Extractor) RemoveSymbols(text string) string {
	return p.Pattern5.ReplaceAllString(text, "")
}

func (p *Extractor) CheckValidDate(date string) bool {
	_, err := time.Parse("Jan 02 2006", date)
	return err == nil
}

func (p *Extractor) IsValidAuthorName(date string) bool {
	return !p.CheckValidDate(date)
}

func (p *Extractor) FormatAuthorName(name string) string {
	return strings.Join(strings.Fields(strings.TrimSpace(name)), " ")
}
