package scraping

import (
	"sort"
	"strings"
	"time"
)

type Sorting struct {
	months map[string]string
}

func NewSorting() *Sorting {
	months := map[string]string{
		"january":   "01",
		"february":  "02",
		"march":     "03",
		"april":     "04",
		"may":       "05",
		"june":      "06",
		"july":      "07",
		"august":    "08",
		"september": "09",
		"october":   "10",
		"november":  "11",
		"december":  "12",
	}

	return &Sorting{months: months}
}

/*func (s *Sorting) OrderingID(news []NewsValueType) []map[string]string {
	for _, individualNews := range news {
		individualNews = uuid.New().String()
	}

	return news
}*/

func (s *Sorting) OrderingDate(individualNews NewsValueType) int {
	if individualNews.Date == "N/A" {
		return 1
	}

	newsDate := strings.ToLower(individualNews.Date)
	newsDate = strings.ReplaceAll(newsDate, ",", "")
	dateParts := strings.Split(newsDate, " ")

	if len(dateParts) != 3 {
		return 1
	}

	month, exists := s.months[dateParts[1]]
	if !exists {
		return 1
	}

	day := dateParts[0]
	year := dateParts[2]
	dateString := year + month + day

	parsedDate, err := time.Parse("20060102", dateString)
	if err != nil {
		return 1
	}

	return int(parsedDate.Unix())
}

func (s *Sorting) OrderingNews(news []NewsValueType) []NewsValueType {
	sort.SliceStable(news, func(i, j int) bool {
		return s.OrderingDate(news[i]) > s.OrderingDate(news[j])
	})
	return news
	//return s.OrderingID(news)
}
