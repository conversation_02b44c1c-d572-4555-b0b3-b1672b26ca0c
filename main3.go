package main

/*
import (
	"bufio"
	"fmt"
	"os"
	"strings"
	"time"
)

type Transaction struct {
	MainTrnId             string    // Tekil işlem numarası
	ShadowCardNumber      string    // Card token
	MaskedCardNumber      string    // Maskeli Kart Numarası
	ChannelType           string    // Kanal Tipi
	SourceNetwork         string    // Kaynak Network
	OriginatorNetworkType string    // İşlemi Başlatan Network Tipi
	TrnProcessType        string    // İşlem Process Tipi
	TrnCode               string    // Transaction Kodu
	TrnCodeDetail         string    // Transaction Kod Detayı
	TrnCodeSpecial        string    // Özel Takas İşlem Kodu
	TrnCodeMatrixId       string    // Tekil işlem kodu - Enum
	RequestDate           time.Time // Talep Tarihi (YYYYMMDD HH:MM:SS.FFF)
	ResponseDate          time.Time // Cevap Tarihi (YYYYMMDD HH:MM:SS.FFF)
	ResponseCode          string    // Cevap Kodu
	ResponseCodeDetail    string    // Cevap Kodu Detayı
	AuthorizationNumber   string    // Otorizasyon Numarası
	OriginalTrnAmount     string    // Orijinal İşlem Tutarı (Soldan 0 padli)
	TrnCurrencyCode       string    // İşlem Kur Kodu
	TrnCurrencyExponent   string    // İşlem Exponent, Kuruş Bilgisi
	TrnAmount             string    // İşlem Tutarı (Soldan 0 padli)
}

func main() {
	// Dosya yolu
	filename := "data.txt"

	// Dosyayı aç
	file, err := os.Open(filename)
	if err != nil {
		fmt.Println("Dosya açılamadı:", err)
		return
	}
	defer file.Close()

	// Satırı satırı oku
	scanner := bufio.NewScanner(file)
	count := 0
	for scanner.Scan() {
		if count == 0 {
			count++
			continue
		}
		line := scanner.Text()
		// Veri satırını işle
		transaction := processLine(line)
		// Transaction struct'ını kullanarak işlem yapabilirsiniz.
		fmt.Println(transaction.MainTrnId)
		break
	}

	if err := scanner.Err(); err != nil {
		fmt.Println("Dosya okunurken bir hata oluştu:", err)
	}
}
func processLine(line string) Transaction {
	transaction := Transaction{}
	parts := strings.Fields(line)

		layout := "20060102 15:04:05.000"
		transactionDateTime, err := time.Parse(layout, parts[2])
		if err != nil {
			fmt.Println("Tarih/Zaman işlenirken bir hata oluştu:", err)
		}

	for _, i := range parts {
		fmt.Println(i)
	}

	return transaction
}
*/
